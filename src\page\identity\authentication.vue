//页面弃用，使用identityCheck.vue替代
<template>
    <div class="authentication">
        <div class="middle"><img src="../../assets/img/faceBg.png" alt=""></div>
        <div class="confirm-btn" @touchstart="confirm">开始认证</div>
        <div class="user-info">请确认是<span class="user-name">{{supName}}</span>操作</div>
    </div>
</template>
<script>
import { initCookie } from '@/api/check'
import { getUserIn } from '@/api/sign'
import { getFaceCode } from '@/api/identity'
export default {
    data () {
        return {
            supName: '',
            businessNo: ''
        }
    },
    mounted () {
        this.$loading(false, '')
        this.getFaceCode()
        this.getUserInfo()
    },
    methods: {
       getFaceDetail () {
           // 网页版不需要人脸识别，直接跳转
           console.log('网页版跳过人脸识别，直接跳转到签到页');
           this.$router.push({
               path: '/sign'
           });
       },
       getFaceCode () {
           // 网页版不需要获取人脸识别码
           console.log('网页版跳过获取人脸识别码');
           this.businessNo = '';
       },
        getUserInfo() {
            getUserIn({}).then((res)=>{
                if (res.errorCode === "0" || res.errorCode === 0) {
                    this.supName = res.data.supName
                } else if (res.errorCode === '1003') {
                     this.getInitCookie()
                } else {
                    this.$toast.center(res.val)
                }
            }).catch((e)=>{
                this.$toast.center(e)
            })
        },
       confirm() {
            this.getFaceDetail()
       },
       getInitCookie () {
            // 网页版不需要认证，直接执行人脸识别（实际上也被跳过）
            console.log('网页版跳过认证');
            this.getFaceDetail();
        },
    }
}
</script>
<style lang="less" scoped>
.authentication{
    overflow: auto;
    height: 100%;
    -webkit-overflow-scrolling: touch;
}
    .middle{
        width:195px;
        height:396px;
        margin:128px auto 81px auto;
        img{
            width:100%;
            height:100%;
        }
    }
    .confirm-btn{
        width:690px;
        height:98px;
        line-height:98px;
        margin:0 auto;
        background:#317dff;
        color:#fff;
        font-size:32px;
        text-align:center;
    }
    .user-info{
        font-size:32px;
        color:#333;
        text-align:center;
        margin-top:50px;
        .user-name{
            color:#317dff;
        }
    }
</style>
