'use strict'
const path = require('path')
module.exports = {
    dev: {
        assetsSubDirectory: 'static',
        assetsPublicPath: '/',
        proxyTable: {
            '/medical-supervision': {
                // 'target': 'https://mss.hfi-health.com:9443/medical-supervision',//生产
                'target': 'http://*************:8088/medical-supervision',
                changeOrigin: true,
                pathRewrite: {
                    '^/medical-supervision': '/'
                }
            },
            '/api2': {
                'target': 'http://*************:8088/medical-supervision', // 本地测试环境
                changeOrigin: true,
                pathRewrite: {
                    '^/api2': '/'
                }
            }
        },
        // host: '*************',//WiFi
        host: '0.0.0.0',
        port: 8182,
        autoOpenBrowser: true,
        errorOverlay: true,
        notifyOnErrors: true,
        poll: false,
        devtool: 'cheap-module-eval-source-map',
        cacheBusting: true,
        cssSourceMap: true
    },
    build: {
        index: path.resolve(__dirname, '../dist/index.html'),
        assetsRoot: path.resolve(__dirname, '../dist'),
        assetsSubDirectory: 'static',
        assetsPublicPath: './',
        productionSourceMap: true,
        devtool: '#source-map',
        productionGzip: false,
        productionGzipExtensions: ['js', 'css'],
        bundleAnalyzerReport: process.env.npm_config_report
    }
}
