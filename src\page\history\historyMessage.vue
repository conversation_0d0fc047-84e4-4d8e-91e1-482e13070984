<template>
    <div class="historyMessage">
        <!--ul里面几个scoller就是无限滚动的几个api-->
        <ul class="mui-table-view" v-if="list.length !== 0" v-infinite-scroll="loadMore" infinite-scroll-disabled="moreLoading" infinite-scroll-distance="0" infinite-scroll-immediate-check="false">
            <!--li数据遍历循环部分-->
            <li class="mui-table-view-cell message-list" v-for="(item,index) in list" :key="index" @click="pageTo('messageDetail',item.mesTitle, item.mesContent)">
                <div class="message-title">{{item.createTime}}</div>
                <div class="message-detail">
                    <div class="message-detail-left">{{item.mesTitle}}</div>
                    <div class="message-detail-right"><img src="../../assets/img/leftArrow.png" alt=""></div>
                </div>
                <div class="message-content">
                    {{item.mesContent}}
                </div>
            </li>
            <!--底部判断是加载图标还是提示“全部加载”-->
            <li class="more_loading" v-show="!queryLoading">
                <mt-spinner type="snake" color="#00ccff" :size="20" v-show="moreLoading&&!allLoaded"></mt-spinner>
                <span v-show="allLoaded || list.length === 0">已全部加载</span>
            </li>
        </ul>
        <div class="no-data" v-else>
            <img src="../../assets/img/nodata.png" alt="">
            <p>暂无消息内容</p>
        </div>
    </div>
</template>
<script>
import { InfintiteScroll } from 'mint-ui'
import { queryMessagePush } from '@/api/history'
import { initCookie } from '@/api/check'
export default {
    data () {
        return {
            list: [],
            queryLoading: false,
            moreLoading: false,
            allLoaded: false,
            totalNum: 0,
            pageSize: 20,
            pageNum: 0
        }
    },
    created() {
        this.$loading(true, '');
        this.loadMore()
    },
    methods:{
        //无限加载函数
        loadMore() {
            if(this.allLoaded){
                this.moreLoading = true;
                return;
            }
            if(this.queryLoading){
                return;
            }
            this.moreLoading = !this.queryLoading;
            this.pageNum++;
            queryMessagePush({
                pageNum: this.pageNum,
                pageSize: this.pageSize,
            }).then((res)=>{
                if(res.errorCode === '0') {
                    this.$loading(false, '');
                    this.list = this.list.concat(res.data);
                    this.allLoaded = res.data.length == 0;
                    console.log(this.allLoaded)
                } else if(res.errorCode === '1003') {
                    this.getInitCookie()
                }
                this.moreLoading = this.allLoaded;
            })
        },
        pageTo (url, title, content) {
            this.$router.push({
                path: url,
                query: {
                    title: title,
                    content: content
                }
            })
        },
         getInitCookie () {
            // 网页版不需要认证，直接加载数据
            this.pageNum = 0;
            this.loadMore();
        },
    },
}
</script>
<style lang="less" scoped>
.historyMessage{
    overflow: auto;
    height: 100%;
    -webkit-overflow-scrolling: touch;
}
.no-data{
    width:487px;
    height:416px;
    margin:299px auto 0 auto;
    background:#fff;
    img{
        width:100%;
        height:100%;
    }
    p{
        text-align:center;
        color:rgba(38,38,40,.4);
        font-size:32px;
        margin-top:18px;
    }
}
.message-list{
     border-bottom:1px solid #ececec;
    .message-title{
        height:88px;
        line-height:88px;
        color:#777;
        font-size:30px;
        background:rgba(124,124,124,0.05);
        padding-left:27px;
    }
    .message-detail{
        display:flex;
        justify-content: space-between;
        height:32px;
        margin-top:32px;
        padding-left:27px;
        font-size:32px;
        color:#333;
        .message-detail-right{
            width:15px;
            height:26px;
            margin-right:29px;
            img{
                width:100%;
                height:100%;
            }
        }
    }
    .message-content{
        width:696px;
        height:40px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-top:20px;
        margin-bottom:26px;
        padding-left:27px;
        font-size:30px;
        color:#777;
    }
}
.more_loading{
    display:flex;
    justify-content: center;
    margin-top:30px;
}
</style>
