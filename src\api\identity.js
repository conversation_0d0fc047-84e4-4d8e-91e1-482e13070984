import { BASE_URL_MEDICAL, AUTH_URL_MEDICAL } from './config';
import axios from 'axios';

// 获取证件二维码
export function getSupervisorQRcode (obj) {
    const url = BASE_URL_MEDICAL + 'supervisor/getSupervisorQRcode';
    return axios.post(url, obj).then((res) => {
      return Promise.resolve(res.data);
    });
}
// 根据二维码id获取用户信息
export function getSupervisorByCode (obj) {
    const url = BASE_URL_MEDICAL + 'supervisor/getSupervisorByCode?code='+ obj.code;
    return axios.get(url).then((res) => {
      return Promise.resolve(res.data);
    });
}

// 获取人脸验证的buissno
export function getFaceCode (obj) {
    const url = AUTH_URL_MEDICAL + 'unite/getFaceRecognitionFlowNumber?authCode='+ obj.authCode;
    return axios.get(url).then((res) => {
      return Promise.resolve(res.data);
    });
}