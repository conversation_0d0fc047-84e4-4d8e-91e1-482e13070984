<template>
<div class="s">
	<div class="s-map" ref="container" id="container"></div>
	<div class="s-input">
		<div class="s2lt"></div>
		<div class="s2ct">
			<input type="text" placeholder="搜索附近机构" v-model="sword" v-mtfocus @focus="focusInput" @blur="blurInput">
			<div class="clearWord" @click="clearWordFn" v-show="showClear"></div>
		</div>
		<div class="s2rt">
			<div class="s2rt-btn" @click="searchFn">
				<span>搜索</span>
			</div>
		</div>
	</div>
	<!-- <div class="historyList">
		<div class="cell">
			<img src="@/assets/img/<EMAIL>">历史纪录
		</div>
	</div> -->
	<div class="s3" v-show="showResultList" ref="list">
		<div class="s3c" ref="ressList">
			<ul>
				<li v-for="(item, index) in resultList" :key="'li' + index" @click="pointDetail(item)">
					<div class="s3cUlt">
						<h3>{{item.orgName}}</h3>
						<p>地址：{{item.orgAddress}}</p>
						<p v-if="item.orgHandleMatter">经办事项：{{item.orgHandleMatter}}</p>
            			<p v-if="item.orgHandleType">经办类型：{{item.orgHandleType}}</p>
            			<p v-if="item.orgWorkTime">工作时间：{{item.orgWorkTime}}</p>
            			<p v-if="item.orgTelephone">联系电话：{{item.orgTelephone}}</p>
					</div>
					<div class="s3cUrt" ></div>
				</li>
			</ul>
		</div>
		<div class="btn" @click="toggleOpen($event)" ref="listBtn"></div>
	</div>
	<div class="historyList" v-show="showHistoryList && historyList.length > 0" ref="historyList">
		<ul ref="historyListUl">
			<li v-for="(item, index) in historyList" :key="'li' + index" @click="selectHistoryWord(item)">
				<!-- <div v-if="item.orgId"> -->
					<div class="s3cUlt">
						<h3 :class="item.orgId ? '':'no-bottom'">{{item.orgName}}</h3>
						<p v-if="item.orgId">地址：{{item.orgAddress}}</p>
					</div>
					<div class="s3cUrt" v-if="item.orgId"></div>
				<!-- </div> -->
				<!-- <div v-else class="s3cUlt">
					<h3>{{item.orgName}}</h3>
				</div> -->
			</li>
		</ul>
		<div class="more clear" v-show="showMoreHis" @click="showMoreHistoryList">
			更多历史记录
		</div>
		<div class="clear" @click="showMsgBox = true">
			清空历史记录
		</div>

	</div>
	<div :class="relLocationBottom ? 'relLocation relLocationBottom' :'relLocation'" :style="{'bottom':relLocBottom +'px'}" @click="backCurrent"><img src="../../assets/img/relLocation.png" alt=""> <span>重新定位</span></div>
    <div class="msgBox" v-show="showMsgBox">
		<div class="msgBox-content">
			<div class="msgBox-header">
				<div class="msgBox-title">
					清空历史记录？
				</div>
				<div class="msgBox-footer">
					<div class="cancel" @click="cancel">
						取消
					</div>
					<div class="spline">
					</div>
					<div class="confirm" @click="confirm">
						立即清空
					</div>
				</div>
			</div>
		</div>
	</div>

</div>
</template>
<script>
import currentImg from '../../assets/img/<EMAIL>'
import pointsImg from '../../assets/img/<EMAIL>'
import dirImg from '../../assets/img/<EMAIL>'
import busImg from '../../assets/img/<EMAIL>'
import carImg from '../../assets/img/<EMAIL>'
import walkImg from '../../assets/img/<EMAIL>'
import moveImg from '../../assets/img/<EMAIL>'
import ydIcon from '@/assets/img/ydIcon.png'
import yyIcon from '@/assets/img/yyIcon.png'
import orgIcon from '@/assets/img/yOrgIcon.png'
import {getOptions,isSMKApp} from '@/assets/js/until.js'
import {initCookie, queryOrganByOrgName, selectOrganInfo, selectOrganInfoNoToken, queryOrganByMyCoordinate} from '../../api/check'
import { getStoredUserLocation, closeWebview } from '@/assets/js/until.js'

export default {
	data () {
		return {
			showMoreHis:false,
			showMsgBox:false,
			map: null,
			current: null,
			// 搜索词
			sword: '',
			// 地图上的定位点
			markerArr: [],
			// 搜索list是否显示
			showResultList: false,
			resultList: [],
			infoBoxArr: [],
			curLocLng: '',
			curLocLat: '',
			movePosition: {
				lng: '',
				lat: ''
			},
			showClear: false,
			positionPicker: null,
			relLocationBottom:true,
			circle:null,
			defaultRelLocBottom:24,
			relLocBottom:24,
			showHistoryList:false,
			historyList:[],
			allHistoryList:[],
			busiStatusOptions:getOptions('busiStatus'),
			orgLevelOptions:getOptions('orgLevel'),
			markerSizeW:30,
			markerSizeH:35,
			lgMarkerSizeW:51,
			lgMarkerSizeH:59.5

		};
	},
	created () {
		this.$loading(true, '');
		let lgS = document.documentElement.clientWidth >= 1080
		if (lgS) {
			this.markerSizeW = 60
			this.markerSizeH = 70
			this.lgMarkerSizeW = 102
			this.lgMarkerSizeH = 119
		}
		
	},
	async mounted () {
		await this.getLocationFromService();
		// this.initMap(30.254925, 120.207493);
	},
	methods: {
		// 新增：使用用户服务获取位置信息
    async getLocationFromService() {
			try {
                console.log('开始获取位置信息(本地)...');
                const locationResult = getStoredUserLocation();
                console.log('位置信息获取结果(本地):', locationResult);

				if (locationResult.success) {
					const { longitude, latitude } = locationResult.location;
					console.log('使用位置:', longitude, latitude, '来源:', locationResult.source);
					this.initMap(latitude, longitude);
				} else {
					console.log('位置获取失败，使用默认位置');
					// 使用默认位置（杭州）
					this.initMap(30.244121753904395, 120.14738921730009);
				}
			} catch (error) {
				console.error('获取位置信息异常:', error);
				// 发生异常时使用默认位置
				this.initMap(30.244121753904395, 120.14738921730009);
			}
		},

		filterOptions (type,value) {
			let currentData = []
           if(type === 'busiStatus'){
			  currentData = this.busiStatusOptions.filter(item=>{
				   return item.key == value
			   })
		   }
		   if(type === 'orgLevel'){
			   currentData = this.orgLevelOptions.filter(item=>{
				   return item.key == value
			   })
		   }
		   return currentData.length > 0 ? currentData[0].value : ''
		},
		showMoreHistoryList(){
			this.showMoreHis = false
			this.historyList = this.getHistoryWords()
			// this.$confirmBox({
			// 				title: '提交',
			// 				content: '两定机构工作人员对检查结果存疑，未做签字确认。',
			// 				callback: function () {
			// 					console.log('提交 saveCheckDetail param=');
			// 				},
			// 				openCkeckSign: function () {
			// 					console.log('去签名 check');
			// 				}
			// 			});
		},
		cancel(){
			this.showMsgBox = false
		},
		confirm(){
			this.removeHistoryWords()
			this.showMsgBox = false
		},
		getHistoryWords(){
			this.allHistoryList = JSON.parse(localStorage.getItem('historySearch')) ? JSON.parse(localStorage.getItem('historySearch')) : []
			return this.allHistoryList
		},
		addHistoryWords(item){
			let allHistoryList = this.getHistoryWords()
			if(allHistoryList.length > 0){
				let currentItem = []
				let findIndex = -1
				if(item.orgId){
					findIndex = allHistoryList.findIndex(filterItem=>{
						return filterItem.orgId == item.orgId
					})
				}else{
					findIndex = allHistoryList.findIndex(filterItem=>{
						return (filterItem.orgName == item.orgName && !filterItem.orgId)
					})
				}
				if(findIndex != -1){
					allHistoryList.splice(findIndex,1)
				}
			}
			allHistoryList.unshift(item)
			localStorage.setItem('historySearch',JSON.stringify(allHistoryList))
		},
		removeHistoryWords(){
			this.historyList = []
			this.allHistoryList = []
			localStorage.removeItem('historySearch')
		},
		focusInput(){
			let allHistoryList = this.getHistoryWords()
			if(allHistoryList.length > 10){
				this.showMoreHis = true
				this.historyList = this.allHistoryList.slice(0,10)
			}else{
				this.showMoreHis = false
				this.historyList = this.allHistoryList
			}
			this.showHistoryList = true
			this.showResultList = false
		},
		selectHistoryWord(item){
			this.showHistoryList = false
			this.sword = item.orgName
			if(item.orgId){
				this.pointDetail(item)
			}else{
				this.searchFn()
			}
		},
		blurInput(){
			// this.showHistoryList = false
		},
		testInit (callback, param) {
			initCookie({authCode: '11111111'}).then((result) => {
				console.log('initCookie', result);
				if (result.errorCode === '0') {
					callback(param);
				}
			})
		},
		// 初始化，网页版不需要认证，直接执行回调
		initCookiefn (callback, param) {
			// 网页版不需要认证，直接执行业务逻辑
			if (callback && typeof callback === 'function') {
				callback(param);
			}
		},

		// 获取当前坐标 - 已替换为新的位置服务
    async getLocation () {
			try {
                console.log('开始获取位置信息(本地)...');
                const locationResult = getStoredUserLocation();
                console.log('位置信息获取结果(本地):', locationResult);

				if (locationResult.success) {
					const { longitude, latitude } = locationResult.location;
					console.log('使用位置:', longitude, latitude, '来源:', locationResult.source);
					this.initMap(latitude, longitude);
				} else {
					console.log('位置获取失败，尝试原有方式...');
					// 给出默认位置
					this.initMap(30.244121753904395, 120.14738921730009);
				}
			} catch (error) {
				console.error('获取位置信息异常:', error);
				// 给出默认位置
				this.initMap(30.244121753904395, 120.14738921730009);
			}
		},





		// // 获取当前坐标
		// getLocation () {
		// 	const _this = this;
		// 	// console.log('获取当前坐标 getLocation');
		// 	if (window.yl.getSystemInfo().appVersion) {
		// 		window.yl.call("getLocation", {}, {
		// 			onSuccess: function (a) {
		// 				console.log('getLocation res', a.param);
		// 				_this.initMap(a.param.latitude, a.param.longitude);
		// 			},
		// 			onFail: function (a) {
		// 				console.log('getLocation onFail=', a);
		// 				_this.$toast.center('未获取到您的定位信息，请到【设置】中开启定位服务');
		// 			}
		// 		})
		// 	} else {
		// 		if(isSMKApp()){
		// 			_this.$toast.center('市民卡')
		// 			window.yl.call("getLocation", {}, {
		// 			onSuccess: function (a) {
		// 				console.log('getLocation res', a.param);
		// 				_this.initMap(a.param.latitude, a.param.longitude);
		// 			},
		// 			onFail: function (a) {
		// 				console.log('getLocation onFail=', a);
		// 				_this.$toast.center('未获取到您的定位信息，请到【设置】中开启定位服务');
		// 			}
		// 		})
		// 		}
		// 		_this.$toast.center('请下载并使用杭州办事APP')
		// 	}
		// },
		// 初始化地图
		initMap (lat, lng, type) {
			const _this = this; 
			_this.curLocLng = lng;
			_this.curLocLat = lat;
			const AMap = window.AMap;
			if (!type) {
			_this.map = new AMap.Map(_this.$refs.container, {
				resizeEnable: false,
				center: new AMap.LngLat(lng, lat),
				zoom: 16
			});
			}
			console.log('****lat,lng==',lat,lng)
			_this.updateCircle(lng, lat)
			// 当前位置
			_this.current = new AMap.Marker({
				icon: new AMap.Icon({
					image: currentImg,
					size: new AMap.Size(24, 24), //图标大小
					imageSize: new AMap.Size(24, 24)
				}),
				map: _this.map,
				position: _this.map.getCenter(),
				zIndex: 200
			});

			_this.map.on('click', function (ev) {
				// console.log('click=', ev.lnglat.lng);
				// _this.showHistoryList = false
				_this.closeSearchList();
				// _this.nearPosition({'lng': ev.lnglat.lng, 'lat': ev.lnglat.lat});
				_this.infoBoxArr.forEach(val => {
					val.close();
				})

			})
			// 拖拽选址
			window.AMapUI.loadUI(['misc/PositionPicker'], function(PositionPicker) {
				console.log('PositionPicker', PositionPicker);
					_this.positionPicker = new PositionPicker({
							mode: 'dragMap',
							map: _this.map,
							iconStyle: {
								url: moveImg,
								size:[20,34],  //要显示的点大小，将缩放图片
								ancher:[10,17],//锚点的位置，即被size缩放之后，图片的什么位置作为选中的位置
							}
					});

					_this.positionPicker.on('success', function(pos){
						console.log('move success', pos);
						_this.updateCircle(pos.position.lng, pos.position.lat)
						if (_this.movePosition.lng && _this.movePosition.lat) {
							var circle = new AMap.Circle({
								fillOpacity: 0,
								strokeOpacity: 0,
								radius: 100,
								center: new AMap.LngLat(_this.movePosition.lng, _this.movePosition.lat)
							});
							circle.setMap(_this.map);
							// console.log('circle.contains(new AMap.LngLat(pos.position.lng, pos.position.lat))', circle.contains(new AMap.LngLat(pos.position.lng, pos.position.lat)));
							if (!circle.contains(new AMap.LngLat(pos.position.lng, pos.position.lat))) {
								_this.movePosition.lng = pos.position.lng;
								_this.movePosition.lat = pos.position.lat;
								_this.nearPosition({'lng': pos.position.lng, 'lat': pos.position.lat});
							}
						} else {
							_this.nearPosition({'lng': pos.position.lng, 'lat': pos.position.lat});
							_this.movePosition.lng = pos.position.lng;
							_this.movePosition.lat = pos.position.lat;
						}
					});
					_this.positionPicker.on('fail', function(pos){
						console.log('move fail', pos);
					})
					_this.positionPicker.start();
			});

			_this.map.on('zoomstart', function (ev) {
				// console.log('zoomstart=', ev);
				_this.closeSearchList();
			})
			// 定义路线方式
			AMap.service("AMap.Driving", function () {
					// 实例化Driving
					_this.driving = new AMap.Driving({
							map: _this.map
					});
			});
            // 公交路线规划
			AMap.service("AMap.Transfer", function () {
					// 实例化Transfer 
					_this.transfer = new AMap.Transfer({
							map: _this.map
					});
			});
            // 步行路线规划
			AMap.service("AMap.Walking", function () {
					// 实例化Walking 
					_this.walking = new AMap.Walking({
							map: _this.map
					});
			});
			_this.nearPosition({'lng': _this.curLocLng, 'lat': _this.curLocLat});
			_this.$loading(false, '');
		},
		updateCircle(lng,lat){
			if(this.circle){
				this.map.remove(this.circle)
			}
			this.circle = new AMap.Circle({
				center: new AMap.LngLat(lng,lat),
				radius: 500, //半径
				borderWeight: 3,
				strokeColor: "#317DFF", 
				strokeOpacity: 0.15,
				// strokeWeight: 6,
				strokeOpacity: 0,
				fillOpacity: 0.15,
				strokeStyle: '',
				// strokeDasharray: [10, 10], 
				// 线样式还支持 'dashed'
				fillColor: '#317DFF',
				zIndex: 50,
			});
			this.circle.setMap(this.map);
		},
		searchFn () {
			if (this.sword !== '') {
				this.showHistoryList = false
				this.addHistoryWords(
					{
						orgName:this.sword
					}
				)
				this.openSearchList();
				this.queryOrganByOrgNameFn(this.sword.trim());
			} else {
				this.$toast.center('请输入关键词')
			}
			this.infoBoxArr.forEach(val => {
				val.close();
			})
		},
		clearWordFn () {
			this.sword = ''
		},
		backCurrent () {
			const fallback = {
				longitude: 120.14738921730009,
				latitude: 30.244121753904395
			};
			try {
				const locationResult = getStoredUserLocation() || {};
				const hasLocation = locationResult.success &&
					locationResult.location &&
					typeof locationResult.location.longitude === 'number' &&
					typeof locationResult.location.latitude === 'number';
				const { longitude, latitude } = hasLocation
					? locationResult.location
					: fallback;
				this.initMap(latitude, longitude);
			} catch (error) {
				console.error('重新定位失败:', error);
				this.initMap(fallback.latitude, fallback.longitude);
			}
		},
		nearPosition (obj) {
			const _this = this;
			const param ={
				'currentLongitude': obj.lng,
				'currentLatitude': obj.lat
			}
			queryOrganByMyCoordinate(param).then(res => {
				console.log('queryOrganByMyCoordinate=', res);
				if (res.errorCode === '0') {
					res.data.forEach(item=>{
						item.busiStatusString = _this.filterOptions('busiStatus',item.busiStatus)
						item.orgLevelString = _this.filterOptions('orgLevel',item.orgLevel)
					})
					console.log(res.data)
					_this.drawMarker(res.data);
				} else if (res.errorCode === '1003'){
					// console.log('1003');
					// _this.testInit(_this.nearPosition, {'lng': obj.lng, 'lat': obj.lat});
					_this.initCookiefn(_this.nearPosition, '');
				} else {
					_this.$toast.center(res.value);
				}
			}).catch(error => {
				_this.$toast.center('接口异常');
			})
		},
		// 精确搜索，只绘制搜索选中的点
		queryOrganByOrgNameFn (v) {
			const _this = this;
			const param = {
				'orgName': v
			}
			queryOrganByOrgName(param).then(res => {
				console.log('queryOrganByOrgName==', res);
				if (res.errorCode === '0') {
					if (res.data && res.data.length > 0) {
						// _this.drawMarker(res.data);
						_this.resultList = res.data;
						_this.showResultList = true;
						_this.$nextTick(()=>{
							_this.$refs.list.scrollTo({top:0})
						})
						
					} else {
						_this.map.remove(_this.markerArr);
						_this.showResultList = false;
						_this.resultList = [];
						_this.$toast.center('查询无结果')
					}
				} else if (res.errorCode === '1003'){
					_this.initCookiefn(_this.queryOrganByOrgNameFn, v)
					// _this.testInit(_this.queryOrganByOrgNameFn, v)
				} else {
					_this.$toast.center(res.value);
				}
			})
		},
		// 在地图上定位点
		drawMarker (points) {
			const _this = this;
			console.log('drawMarker', points);
			let pointArr = [];
			const AMap = window.AMap;
			_this.map.remove(_this.markerArr);
			// 路线
			points.forEach((el, index) => {
				pointArr[index] = new AMap.LngLat(el.orgLongitude, el.orgLatitude);
				_this.markerArr[index] = new AMap.Marker({
						icon: new AMap.Icon({
								// image: pointsImg,
								image: el.orgType == 1 ? ydIcon : el.orgType == 0 ? yyIcon : orgIcon,
								// size: new AMap.Size(15, 19),  //图标大小
								// imageSize: new AMap.Size(15, 19)
								size: new AMap.Size(this.markerSizeW, this.markerSizeH),  //图标大小
								imageSize: new AMap.Size(this.markerSizeW, this.markerSizeH)
						}),
						// offset: new AMap.Pixel(-7,-19),
						offset: new AMap.Pixel(-15,-35),
						map: _this.map,
						position: pointArr[index],
						zIndex: 200
				});
				// 自定义窗体
				_this.infoBoxArr[index] = new AMap.InfoWindow({
						isCustom: true //使用自定义窗体
				});
				// 当信息窗关闭时，重置图标
				_this.infoBoxArr[index].on('close', function(){
					_this.markerArr[index].setIcon(new AMap.Icon({
							// image: pointsImg,
							image: el.orgType == 1 ? ydIcon : el.orgType == 0 ? yyIcon : orgIcon,
							// size: new AMap.Size(15, 19),  //图标大小
							// imageSize: new AMap.Size(15, 19)
							size: new AMap.Size(this.markerSizeW, this.markerSizeH),  //图标大小
							imageSize: new AMap.Size(this.markerSizeW, this.markerSizeH)
						}));
					// _this.markerArr[index].setOffset(new AMap.Pixel(-7,-19));
					_this.markerArr[index].setOffset(new AMap.Pixel(-15,-35));
				})
				//绑定事件一次
				_this.markerArr[index].evtFlag = true
				_this.markerArr[index].i1evtFlag = true
				_this.markerArr[index].i2evtFlag = true
				_this.markerArr[index].i3evtFlag = true
				// marker点击事件
        AMap.event.addListener(_this.markerArr[index], 'click', function () {
			        console.log('点击了标记');
					// _this.map.setCenter(_this.markerArr[index].getPosition());
					_this.transfer && _this.transfer.clear();
					_this.driving && _this.driving.clear();
					_this.walking && _this.walking.clear();
					_this.closeResList();
					const param = {
						'orgId': el.orgId,
						'currentLongitude': _this.curLocLng,
						'currentLatitude': _this.curLocLat
					}
					selectOrganInfo(param).then(organInfoRes => {
						console.log('marker click selectOrganInfo=', organInfoRes);
						_this.markerArr[index].evtFlag = true
						_this.markerArr[index].i1evtFlag = true
						_this.markerArr[index].i2evtFlag = true
						_this.markerArr[index].i3evtFlag = true
						if (organInfoRes.errorCode === '0') {
							organInfoRes.data.orgLevelString = _this.filterOptions('orgLevel',organInfoRes.data.orgLevel)
							organInfoRes.data.busiStatusString = _this.filterOptions('busiStatus',organInfoRes.data.busiStatus)
							_this.clickCurrentPointFn (el.orgId, organInfoRes.data, false, index);
						} else if (organInfoRes.errorCode === '1003') {
							// 游客
							selectOrganInfoNoToken(param).then(yRes => {
								console.log('游客 click selectOrganInfoNoToken=', yRes);
								if (yRes.errorCode === '0') {
									yRes.data.orgLevelString = _this.filterOptions('orgLevel',yRes.data.orgLevel)
									yRes.data.busiStatusString = _this.filterOptions('busiStatus',yRes.data.busiStatus)
									_this.clickCurrentPointFn (el.orgId, yRes.data, true, index);
								} else {
									_this.$toast.center(yRes.value)
								}
							})
						} else {
							_this.$toast.center(organInfoRes.value)
						}
					})
				});
				// // 信息框open事件
				// _this.infoBoxArr[index].on('open', function (el) {
				// 	// 信息框里的事件
				// 	_this.showImgEvt(index);
				// 	_this.img1Evt(index);
				// 	_this.img2Evt(index);
				// 	_this.img3Evt(index);
				// })
				// over
			});
		},
		// 根据经纬度开启详情
		pointDetail (item) {
			console.log('搜索中的导航');
			const _this=this;
			_this.$loading(true, '');
			_this.closeResList();
			// 停止拖拽选点
			_this.positionPicker.stop();
			_this.addHistoryWords(item)
			if (_this.curLocLng && _this.curLocLat) {
				const param = {
					'orgId': item.orgId,
					'currentLongitude': _this.curLocLng,
					'currentLatitude': _this.curLocLat
				}
				selectOrganInfo(param).then(res => {
					console.log('search list click selectOrganInfo=', res);
					_this.$loading(false, '');
					if (res.errorCode === '0') {
						res.data.busiStatusString = _this.filterOptions('busiStatus',res.data.busiStatus)
						res.data.orgLevelString = _this.filterOptions('orgLevel',res.data.orgLevel)
						_this.enterCurrentPointFn(item, res.data, false);
					} else if (res.errorCode === '1003') {
						// 游客
						_this.$loading(true, '');
						selectOrganInfoNoToken(param).then(yRes => {
							console.log('游客 selectOrganInfoNoToken=', yRes);
							_this.$loading(false, '');
							if (yRes.errorCode === '0') {	
								yRes.data.busiStatusString = _this.filterOptions('busiStatus',yRes.data.busiStatus)
						        yRes.data.orgLevelString = _this.filterOptions('orgLevel',yRes.data.orgLevel)
								_this.enterCurrentPointFn(item, yRes.data, true);
							} else {
								_this.$toast.center(yRes.value)
							}
						})
					} else {
						_this.$toast.center(res.value)
					}
				})
			} else {
				_this.$toast.center('无法定位')
			}
		},
		// 结果list
		closeResList () {
			this.showResultList =false
		},
		showImgEvt (index) {
			console.log('index:' + index);
			const _this = this;
			console.log('_this.markerArr[index].evtFlag', _this.markerArr[index].evtFlag);
			if (_this.markerArr[index].evtFlag) {		
				_this.markerArr[index].evtFlag = false;
				document.querySelector('#showImg'+index).addEventListener('click', function(){
					console.log('执行一次1');
					let itemList = document.querySelector('.mapSCs2');
					if (itemList.classList.contains('mapSCs2act')) {
						itemList.classList.remove('mapSCs2act');
						console.log('执行一次2');
					} else {
						itemList.classList.add('mapSCs2act');
						console.log('执行一次3');
					}
				}, false)
			}
		},
		// 驾车
		img1Evt (index) {
			console.log('驾车');
			const _this = this;
			const AMap = window.AMap;
			if (_this.markerArr[index].i1evtFlag) {
				_this.markerArr[index].i1evtFlag = false;
				document.querySelector('#imga'+index).addEventListener('click', function(el){
					console.log('driving click');
					const evt = this;
					_this.transfer && _this.transfer.clear();
					_this.walking && _this.walking.clear();
					_this.driving.search(new AMap.LngLat(_this.curLocLng, _this.curLocLat), new AMap.LngLat(evt.dataset.lng, evt.dataset.lat),function(status, result){
							if (status === 'complete') {
								console.log('驾车 complete')
							} else {
								console.log('获取驾车路线失败' + result)
								_this.$toast.center('获取路线失败');
							}
						});
				}, false)
			}
		},
		// 公交
		img2Evt (index) {
			console.log('公交');
			const _this = this;
			const AMap = window.AMap;
			if (_this.markerArr[index].i2evtFlag) {
				_this.markerArr[index].i2evtFlag = false;
				document.querySelector('#imgb'+index).addEventListener('click', function(el){
					console.log('bus click');
					const evt = this;
					_this.walking && _this.walking.clear();
					_this.driving && _this.driving.clear();
					var tDistance = _this._GetDistance(evt.dataset.lat, evt.dataset.lng, _this.curLocLat, _this.curLocLng)
					if (tDistance > 0.4) {
						_this.transfer.search(new AMap.LngLat(_this.curLocLng, _this.curLocLat), new AMap.LngLat(evt.dataset.lng, evt.dataset.lat),function(status, result){
							if (status === 'complete') {
								console.log('公交 complete')
							} else {
								console.log('获取公交路线失败' + result)
								_this.$toast.center('获取路线失败');
							}
						});
					} else {
						_this.$toast.center('距离太近，请步行');
					}
				}, false)
			}
		},
		// 步行
		img3Evt (index) {
			console.log('步行');
			const _this = this;
			const AMap = window.AMap;
			if (_this.markerArr[index].i3evtFlag) {
				_this.markerArr[index].i3evtFlag = false;
				document.querySelector('#imgc'+index).addEventListener('click', function(el){
					console.log('walk click');
					const evt = this;
					_this.transfer && _this.transfer.clear();
					_this.driving && _this.driving.clear();
					_this.walking.search(new AMap.LngLat(_this.curLocLng, _this.curLocLat), new AMap.LngLat(evt.dataset.lng, evt.dataset.lat),function(status, result){
							if (status === 'complete') {
								console.log('步行 complete')
							} else {
								console.log('获取步行路线失败' + result)
								_this.$toast.center('获取路线失败');
							}
						});
				}, false)
			}
		},
		// checkNum:[true | false] 游客没有检查次数
		enterCurrentPointFn (item, el, checkNum) {
			console.log(item);
			console.log(el);
			console.log(checkNum);

			const _this = this;
			const AMap = window.AMap;
			var onePointArr = [];
			onePointArr.push(item)
			_this.drawMarker(onePointArr);	
			_this.infoBoxArr[0].setContent(`
			<div class="mapSC" id="mapSC">
				<div class="mapSCp">
					<h2><span class="${el.busiStatusString||el.orgLevelString ? 'orgName' : 'orgName noright' }">${el.orgName}</span><span class="busiStatus" style="display:${el.busiStatusString ? 'inline-block' : 'none' }">${el.busiStatusString}</span><span class="orgLevel" style="display:${el.orgLevelString ? 'inline-block' : 'none' }">${el.orgLevelString}</span></h2>
					<p class="p1">${el.orgAddress}</p>
					<p class="p2">${parseInt(el.distance) >1000? parseInt(parseInt(el.distance)/100)/10 +'km' : parseInt(el.distance)+'m'}
						<span style="display:${el.areasName ? 'inline-block' : 'none' }">|</span>${el.areasName? el.areasName: ''}
					</p>
					<p class="p1" style="display:${el.orgHandleMatter ? 'block' : 'none' }">经办事项：${el.orgHandleMatter?el.orgHandleMatter:''}</p>
					<p class="p1" style="display:${el.orgHandleType ? 'block' : 'none' }">经办类型：${el.orgHandleType?el.orgHandleType:''}</p>
					<p class="p1" style="display:${el.orgWorkTime ? 'block' : 'none' }">工作时间：${el.orgWorkTime?el.orgWorkTime:''}</p>
					<p class="p1" style="display:${el.orgTelephone ? 'block' : 'none' }">联系电话：${el.orgTelephone?el.orgTelephone:''}</p>								
					<p class="p3" style="display:${checkNum ? 'none' : 'block'};">历史检查：${el.historyCheckNum}次</p>
				</div>
				<div class="mapSCs">
					<div class="mapSCs1" id="showImg0"><img src="${dirImg}"/></div>
					<div class="mapSCs2">
						<div class="img img1" data-lng="${el.orgLongitude}" data-lat="${el.orgLatitude}" id="imga0"><img src="${carImg}"/></div>
						<div class="img img2" data-lng="${el.orgLongitude}" data-lat="${el.orgLatitude}" id="imgb0"><img src="${busImg}"/></div>
						<div class="img img3" data-lng="${el.orgLongitude}" data-lat="${el.orgLatitude}" id="imgc0"><img src="${walkImg}"/></div>
					</div>
				</div>
			</div>
			`);
			
			// 当前定位图标变大
			_this.markerArr[0].setIcon(new AMap.Icon({
				image: pointsImg,
				size: new AMap.Size(33, 42),  //图标大小
				imageSize: new AMap.Size(33, 42)
			}));
			_this.markerArr[0].setOffset(new AMap.Pixel(-16,-42));

			_this.relLocationBottom = false
			_this.infoBoxArr[0].open(_this.map, new AMap.LngLat(item.orgLongitude, item.orgLatitude));
			_this.map.setCenter(new AMap.LngLat(item.orgLongitude, item.orgLatitude));

			setTimeout(function(){
				//信息框里的事件
				_this.showImgEvt(0);
				_this.img1Evt(0);
				_this.img2Evt(0);
				_this.img3Evt(0);
			},100)


			// 关闭弹窗
			_this.closeResList();
			var mapSC = document.getElementById('mapSC')
			_this.relLocBottom = mapSC.clientHeight  + _this.defaultRelLocBottom
		},

		clickCurrentPointFn (orgId, info, checkNum, index) {
			console.log('创建自定义窗体')
			console.log(info);
			console.log(checkNum);
			console.log(index);
           
			const _this = this;
			const AMap = window.AMap;
		    // 使当前定位图标变大
            _this.markerArr[index].setIcon(new AMap.Icon({
					// image: pointsImg,
					image: info.orgType == 1 ? ydIcon : info.orgType == 0 ? yyIcon : orgIcon,
					// size: new AMap.Size(30, 35),  //图标大小
					// imageSize: new AMap.Size(30, 35)
					size: new AMap.Size(this.lgMarkerSizeW, this.lgMarkerSizeH),  //图标大小
					imageSize: new AMap.Size(this.lgMarkerSizeW, this.lgMarkerSizeH)
				}));
			_this.markerArr[index].setOffset(new AMap.Pixel(-25.5,-59.5));


			_this.infoBoxArr[index].setContent(`
			<div class="mapSC" id="mapSC">
				<div class="mapSCp">
					<h2><span class="${info.busiStatusString||info.orgLevelString ? 'orgName' : 'orgName noright' }">${info.orgName}</span><span class="busiStatus" style="display:${info.busiStatusString ? 'inline-block' : 'none' }">${info.busiStatusString}</span><span class="orgLevel" style="display:${info.orgLevelString ? 'inline-block' : 'none' }">${info.orgLevelString}</span></h2>
					<p class="p1">${info.orgAddress}</p>
					<p class="p2">${parseInt(info.distance) >1000? parseInt(parseInt(info.distance)/100)/10 +'km' : parseInt(info.distance)+'m'}
						<span style="display:${info.areasName ? 'inline-block' : 'none' }">|</span>${info.areasName? info.areasName: ''}
					</p>
					<p class="p1" style="display:${info.orgHandleMatter ? 'block' : 'none' }">经办事项：${info.orgHandleMatterd?info.orgHandleMatterd:''}</p>
					<p class="p1" style="display:${info.orgHandleType ? 'block' : 'none' }">经办类型：${info.orgHandleType?info.orgHandleType:''}</p>
					<p class="p1" style="display:${info.orgWorkTime ? 'block' : 'none' }">工作时间：${info.orgWorkTime?info.orgWorkTime:''}</p>
					<p class="p1" style="display:${info.orgTelephone ? 'block' : 'none' }">联系电话：${info.orgTelephone?info.orgTelephone:''}</p>
					<p class="p3" style="display:${checkNum ? 'none' : 'block'};">历史检查：${info.historyCheckNum}次</p>
				</div>
				<div class="mapSCs">
					<div class="mapSCs1" id="showImg${index}"><img src="${dirImg}"/></div>
					<div class="mapSCs2">
						<div class="img img1" data-lng="${info.orgLongitude}" data-lat="${info.orgLatitude}" id="imga${index}"><img src="${carImg}"/></div>
						<div class="img img2" data-lng="${info.orgLongitude}" data-lat="${info.orgLatitude}" id="imgb${index}"><img src="${busImg}"/></div>
						<div class="img img3" data-lng="${info.orgLongitude}" data-lat="${info.orgLatitude}" id="imgc${index}"><img src="${walkImg}"/></div>
					</div>
				</div>
			</div>
			`);
			_this.relLocationBottom = false
			_this.infoBoxArr[index].open(_this.map, _this.markerArr[index].getPosition());

			// _this.markerArr[index].setIcon(new AMap.Icon({
			// 		// image: pointsImg,
			// 		image: info.orgType == 1 ? ydIcon : yyIcon,
			// 		// size: new AMap.Size(30, 35),  //图标大小
			// 		// imageSize: new AMap.Size(30, 35)
			// 		size: new AMap.Size(51, 59.5),  //图标大小
			// 		imageSize: new AMap.Size(51, 59.5)
			// 	}));
			// _this.markerArr[index].setOffset(new AMap.Pixel(-25.5,-59.5));


			setTimeout(function(){
				//信息框里的事件
				_this.showImgEvt(index);
				_this.img1Evt(index);
				_this.img2Evt(index);
				_this.img3Evt(index);
			},100)
			
			// //信息框里的事件
			// _this.showImgEvt(index);
			// _this.img1Evt(index);
			// _this.img2Evt(index);
			// _this.img3Evt(index);
			
			var mapSC = document.getElementById('mapSC')
			_this.relLocBottom = mapSC.clientHeight  + _this.defaultRelLocBottom
		},
		toggleOpen () {
			const listBox = this.$refs.list;
			const listBtn = this.$refs.listBtn;
			// listBtn
			if (listBox.classList.contains('s3Act')) {
				listBox.classList.remove('s3Act');
				listBtn.innerHTML='';
			} else {
				listBox.classList.add('s3Act');
				listBtn.innerHTML='点击查看更多结果';
			}
		},
		openSearchList () {
			const listBox = this.$refs.list;
			const listBtn = this.$refs.listBtn;
			// listBtn
			if (listBox.classList.contains('s3Act')) {
				listBox.classList.remove('s3Act');
				listBtn.innerHTML='';
			}
		},
		// 关闭查询列表
		closeSearchList () {
			const listBox = this.$refs.list;
			const listBtn = this.$refs.listBtn;
			this.relLocationBottom = true
			this.relLocBottom = this.defaultRelLocBottom
			// listBtn
			if (!listBox.classList.contains('s3Act')) {
				this.relLocBottom = this.defaultRelLocBottom * 2
				listBox.classList.add('s3Act');
				listBtn.innerHTML='点击查看更多结果';
			}
			this.infoBoxArr.forEach(val => {
				val.close();
			})
		},
		_throttle (fn, context, delay, text) {
			clearTimeout(fn.timeoutId);
			fn.timeoutId = setTimeout(function() {
					fn.call(context, text);
			}, delay);
		},
		_getRad(d) {
    	return d * Math.PI / 180.0;
		},
		_GetDistance(lat1, lng1, lat2, lng2) {
			const _this = this;
			var radLat1 = _this._getRad(lat1);
			var radLat2 = _this._getRad(lat2);
			var a = radLat1 - radLat2;
			var b = _this._getRad(lng1) - _this._getRad(lng2);
			var s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(radLat1) * Math.cos(radLat2) *
					Math.pow(Math.sin(b / 2), 2)));
			s = s * 6378.137;
			s = Math.round(s * 10000) / 10000;
			return s;
		}
	},
	directives: {
		'mtfocus' (el, binding, vnode) {
			el.onfocus = function () {
				let itemList = document.querySelector('.mapSCs2');
				if (itemList && itemList.classList.contains('mapSCs2act')) {
					itemList.classList.remove('mapSCs2act');
				}
			}
		}
	},
	components: {
	},
	watch: {
		sword(nv, ov){
			const _this = this;
			if (nv === '') {
				this.showClear = false
			} else {
				this.showClear = true
			}
		}
	}
};
</script>
<style lang="less" scoped>
@import '../../assets/css/mixin.less';
.historyList {
	position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 0 0 0.4rem;
    background: #fff;
    z-index: 1000;
	transition: all 0.3s linear;
	height: 60%;
	overflow-y: auto;
	-webkit-overflow-scrolling: touch;
	ul{
		overflow-y: auto;
		li{
			// div{
			padding: 0 20px 0 30px;
			display: flex;
			position: relative;
			&::after{
				.setBottomLine(#ECECEC);
			}
			.s3cUlt{
				flex: auto;
				overflow: hidden;
				padding: 23px 0 25px 0;
				margin-right: 20px;
				h3{
					font-size: 34px;
					color: #333;
					padding-bottom: 27px;
					font-weight: bold;
				}
				.no-bottom {
					padding-bottom: 0;
				}
				P{
					font-size: 28px;
					color: #333;
				}
			}
			.s3cUrt{
				flex: 0 0 84px;
				background: url(../../assets/img/<EMAIL>) no-repeat right center;
				background-size: 84px 84px;
			}
			// }
		}
	}
	.more {
		position: relative;
		&::after{
			.setBottomLine(#ECECEC);
		}
		
	}
	.clear {
		color: #777777;
		font-size: 28px;
		text-align: center;
		padding: 27px;
	}
}
.s{
	width: 100%;
	height: 100%;
	position: relative;
	line-height: 1.5;
	.s-map{
		width: 100%;
		height: 100%;
	}
	.s-input{
		position: absolute;
		left: 50%;
		top:30px;
		transform: translateX(-50%);
		width: calc(100% - 60px);
		height: 88px;
		padding-left: 12px;
		background-color: #fff;
		border-radius:44px;
		display: flex;
		overflow: hidden;
		box-shadow:0px 3px 13px 0px rgba(0, 0, 0, 0.18);
		z-index: 100;
		.s2lt{
			flex: 0 0 88px;
			height: 88px;
			background: url(../../assets/img/<EMAIL>) no-repeat center center;
			background-size: 32px 33px;
		}
		.s2rt{
			flex: 0 0 156px;
			height: 88px;
			.s2rt-btn{
				height: 60px;
				padding: 14px 17px;
				span{
					display: block;
					width: 100%;
					height: 100%;
					line-height: 60px;
					text-align: center;
					background: #317DFF;
					border-radius: 30px;
					font-size: 32px;
					font-weight: 500;
					color: #fff;
				}
			}
		}
		.s2ct{
			flex: auto;
			position: relative;
			input{
				border: none;
				font-size: 30px;
				width: 80%;
				height: 100%;
				outline: none;
				line-height: 1.5;
				font-weight: 500;
				padding: 0;
				margin: 0;
				&::placeholder{
					color: #777;
				}
			}
			.clearWord{
				position: absolute;
				width: 32px;
				height: 32px;
				right: 20px;
				top: 50%;
				margin-top: -16px;
				background: url(../../assets/img/<EMAIL>) no-repeat center center;
				background-size: 32px 32px;
			}
		}
	}
	.s3{
		position: absolute;
		bottom: 0;
		left: 0;
		width: 100%;
		max-height: 60%;
		padding: 30px 0;
		padding-top: 0px;
		background: #fff;
		z-index: 1000;
		overflow: scroll;
		transition: all 0.3s linear;
		.btn{
			position: absolute;
			bottom: 0;
			left: 0;
			width: 100%;
			height: 60px;
			line-height: 60px;
			font-size: 32px;
			color: #666;
			text-align: center;
      		padding-top: 5px;
      		background-color: #fff;
      		padding-bottom: calc( constant(safe-area-inset-bottom) );
      		padding-bottom: calc( env(safe-area-inset-bottom) );
		}
		.s3c{
			width: 100%;
			height: 100%;
			z-index: 12;
			overflow: scroll;
			-webkit-overflow-scrolling: touch;
			ul{
				li{
					padding: 0 20px 0 30px;
					display: flex;
					position: relative;
					&::after{
						.setBottomLine(#ECECEC);
					}
					.s3cUlt{
						flex: auto;
						// max-width: 616px;
						overflow: hidden;
						padding: 23px 0 25px 0;
						margin-right: 20px;
						h3{
							font-size: 34px;
							color: #333;
							padding-bottom: 12px;
							font-weight: bold;
						}
						P{
							font-size: 28px;
							color: #333;
						}
					}
					.s3cUrt{
						flex: 0 0 84px;
						background: url(../../assets/img/<EMAIL>) no-repeat right center;
						background-size: 84px 84px;
					}
				}
			}
		}
	}
	.s3Act{
		height: 0;
		transition: all 0.3s linear;
    	padding: 0;

	}
	.relLocation{
		position:absolute;
		bottom:400px;
		left:27px;
		display:flex;
		justify-content: center;
		align-items: center;
		background:#fff;
		box-shadow:0px 2px 8px 1px rgba(179,180,189,0.25);
		width:204px;
		height:56px;
		// border:1px solid #ddd;
		border-radius:30px;
		color:#333333;
		font-size:30px;
    	margin-bottom: calc( constant(safe-area-inset-bottom) + 20px );
    	margin-bottom: calc( env(safe-area-inset-bottom) + 20px );
		img{
			width:32px;
			height:32px;
			margin-right:10px;
		}
	}

	.relLocationBottom {
		bottom:48px;
	}
}
.msgBox {
	position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
	background-color: rgba(0, 0, 0, 0.3);
	z-index: 1000;
	&-content {
		// position: absolute;
		height: 345px;
		text-align: center;
		position: absolute;
		top: 432px;
		left: 63px;
		// transform: translate3d(-50%, -50%, 0);
		width: calc(100% - 126px);
		background: white;
		border-radius: 20px;
	}
	&-header {
		height: 256px;
		line-height: 256px;
	}
	&-title {
		font-size: 34px;
		// padding: 122px 30px;
		color: #333333;
		border-bottom: 1px solid #ececec;
	}
	&-footer {
		display: flex;
		/* padding: 10px; */
		height: 88px;
		/* border-right: 1px solid #ececec; */
		align-items: center;
		color: #333333;
		font-size: 32px;
		.cancel {
			flex: 1;
		}
		.confirm {
			flex: 1;
			color: #317DFF;
		}
		.spline {
			width: 1px;
			background: #ececec;
			height: 100%;
		}
	}
}

.messageBoxWrap {
	z-index:1001 !important;
}
.messageBoxTitle h1 {
	padding: 122px 0 122px 0;
	font-size: 34px;
	font-weight: 500;
}
.messageBoxContent {
	padding-bottom: 0;
}
.messageBoxButton {
	height: 45px;
}

</style>
