import { BASE_URL_MEDICAL } from './config';
import axios from 'axios';
import { aesEncrypt } from '@/assets/js/until.js';
const system = 'system/';
const supervisor = 'supervisor/';
const check = 'check/';
const file = 'file/';
// 通过authToken和origin获取用户token
// 固定访问指定域名，不受环境配置影响
export function getJtToken (obj) {
  // 根据环境使用不同的URL配置
  let url;
  if (process.env.NODE_ENV === 'development') {
    // 开发环境使用代理
    url = '/medical-supervision/Core/thirdAccess/getJtToken';
  } else {
    // 生产环境使用nginx代理的路径
    url = '/hzAppMS/Core/thirdAccess/getJtToken';
  }
  console.log('getJtToken 访问URL:', url, 'Environment:', process.env.NODE_ENV);
  return axios.post(url, obj).then((res) => {
    return Promise.resolve(res.data);
  });
}

// 初始化cooike
export function initCookie (obj) {
  const url = BASE_URL_MEDICAL + system + 'initCookie';
  return axios.post(url, obj).then((res) => {
    return Promise.resolve(res.data);
  });
}
// https://test.iconntech.com/supervisor/getUserInfo
// 获取用户信息
// 现在从后端获取用户详细信息，返回格式保持不变
export function getUserInfo () {
  const url = BASE_URL_MEDICAL + supervisor + 'getUserInfo';
  return axios.post(url).then((res) => {
    // 返回的格式保持不变：{ errorCode: "0", data: { supName, supPhoto, ... } }
    return Promise.resolve(res.data);
  });
}

// 获取用户信息（带AES加密参数）
// 自动对传入的手机号进行AES加密
// 参数是手机号字符串
export function getUserInfoByEnc (phone) {
  const url = BASE_URL_MEDICAL + supervisor + 'getUserInfo';

  // 对手机号字符串进行AES加密
  const encrypted = aesEncrypt(phone);

  // 返回加密后的数据
  return axios.post(url, { encoder: encrypted }).then((res) => {
    return Promise.resolve(res.data);
  });
}

// 检查项目签字
export function saveCheckDetail (obj) {
  const url = BASE_URL_MEDICAL + check + 'saveCheckDetail';
  return axios.post(url, obj).then((res) => {
    return Promise.resolve(res.data);
  });
}

// 检查结果录入
export function updateCheck (obj) {
  const url = BASE_URL_MEDICAL + check + 'updateCheck';
  return axios.post(url, obj).then((res) => {
    return Promise.resolve(res.data);
  });
}

// 获取检查项目列表
export function queryCheckList (obj) {
  const url = BASE_URL_MEDICAL + check + 'queryCheckList';
  return axios.post(url, obj).then((res) => {
    return Promise.resolve(res.data);
  });
}
// 文件上传
export function upload (obj) {
  const url = BASE_URL_MEDICAL + file + 'upload';
  return axios.post(url, obj).then((res) => {
    return Promise.resolve(res.data);
  });
}
// 获取文件上传token
export function getFileToken (obj) {
  const url = BASE_URL_MEDICAL + file + 'getFileToken';
  return axios.post(url, obj).then((res) => {
    return Promise.resolve(res.data);
  });
}

// 历史记录详情页
export function queryCheckDetailList (obj) {
  const url = BASE_URL_MEDICAL + check + 'queryCheckDetailList';
  return axios.post(url, obj).then((res) => {
    return Promise.resolve(res.data);
  });
}

// 当前屏幕机构列表查询
export function queryOrganOnMyScreen (obj) {
  const url = BASE_URL_MEDICAL + supervisor + 'queryOrganOnMyScreen';
  return axios.post(url, obj).then((res) => {
    return Promise.resolve(res.data);
  });
}

// 机构列表查询
export function queryOrganByOrgName (obj) {
  const url = BASE_URL_MEDICAL + supervisor + 'queryOrganByOrgName';
  return axios.post(url, obj).then((res) => {
    return Promise.resolve(res.data);
  });
}

// 查询机构详情
export function selectOrganInfo (obj) {
  const url = BASE_URL_MEDICAL + supervisor + 'selectOrganInfo';
  return axios.post(url, obj).then((res) => {
    return Promise.resolve(res.data);
  });
}

// 查询机构详情
export function selectOrganInfoNoToken(obj) {
  const url = BASE_URL_MEDICAL + supervisor + 'selectOrganInfoNoToken';
  return axios.post(url, obj).then((res) => {
    return Promise.resolve(res.data);
  });
}

// 查询机构详情
export function queryOrganByMyCoordinate (obj) {
  const url = BASE_URL_MEDICAL + supervisor + 'queryOrganByMyCoordinate';
  return axios.post(url, obj).then((res) => {
    return Promise.resolve(res.data);
  });
}
