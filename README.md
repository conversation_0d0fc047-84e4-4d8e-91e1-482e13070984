# demo

> A Vue.js project

## Build Setup

``` bash
# install dependencies
npm install

# serve with hot reload at localhost:8080
npm run dev

# build for production with minification
npm run build

# build for production and view the bundle analyzer report
npm run build --report
```

项目介绍
1、使用vue + vuerouter + webpack + babel + less + axios + vuex + ES6 创建项目
## 插件 lib-flexible  移动端rem
    cnpm install lib-flexible --save-dev
##自动将px转换成rem的插件
    cnpm install px2rem-loader --save-dev
## 安装axios 
    cnpm install axios --save-dev
### 安装less
    cnpm i less less-loader --save-dev
## 安装vuex 
    cnpm i vuex --save-dev

## 路由懒加载的两种方式
    component: resolve => {require(['@/components/HelloWorld'],resolve)}
  ## 此项目使用这种懒加载方式 const helloWorld = () =>import('@/components/HelloWorld'])

## css样式统一 
    统一使用 ： style-class的形式，不适用驼峰
## 路由命名同样使用驼峰命名方式，文件命名统一使用驼峰命名
    如： userDetailk
