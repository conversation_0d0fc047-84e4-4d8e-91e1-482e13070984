# Nginx配置修改建议

## 当前问题分析
你的nginx配置中，`/hzAppMS/`路径的代理设置可能导致路径重复或不匹配。

## 建议的nginx配置修改

```nginx
upstream hzAppMsBackA {
    server 192.10.13.151:29001;
    server 192.10.13.155:29001;
    server 192.10.13.170:29001;
}

server {
    listen 9443;
    server_name _;
    access_log /data/wwwlogs/9443_access.log combined;

    error_page 503 /500.html;
    location /500.html {
        alias /data/wwwroot/9443/500.html;
    }

    # 前端静态文件
    location /hzbs-medical/ {
        alias /data/wwwroot/hzbs-medical/;
        try_files $uri $uri/ /hzbs-medical/index.html;
    }

    # 代理 hzAppMS 后端 API（修改后）
    location /hzAppMS/ {
        # 使用upstream进行负载均衡
        proxy_pass http://hzAppMsBackA/hzAppMS/;
        # 或者如果后端不需要/hzAppMS前缀，使用以下配置：
        # proxy_pass http://hzAppMsBackA/;
        
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header clientVisitIp $remote_addr;
        
        # 添加CORS支持
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
        add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';
        
        if ($request_method = 'OPTIONS') {
            return 204;
        }
    }
}
```

## 关键修改点

1. **使用upstream负载均衡**：替换单一服务器为upstream配置
2. **添加必要的proxy headers**：确保后端能正确识别请求来源
3. **添加CORS支持**：解决跨域问题
4. **处理OPTIONS请求**：支持预检请求

## 测试步骤

1. 修改nginx配置后重启nginx：`nginx -s reload`
2. 检查nginx配置是否正确：`nginx -t`
3. 测试API接口：`curl -X POST http://your-domain:9443/hzAppMS/Core/thirdAccess/getJtToken`
